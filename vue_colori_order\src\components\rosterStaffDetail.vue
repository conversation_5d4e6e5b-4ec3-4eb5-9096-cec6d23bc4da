<template>
  <div style="text-align: left;">
    <van-sticky :offset-top="0">
      <van-nav-bar title="详细信息" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-tabs v-model="activeName" color="#1989fa" sticky @change="onChange">
      <van-tab title="基本信息" name="1">
        <div style="margin-left:2%;margin-top:2%;">
          <p style="font-weight: 900;font-size: 16px;">照片</p>
          <div class="photo">
            <img :src="fileList" width="100%" height="100%" />
          </div>
          <p style="font-weight: 900;font-size: 16px;">基本信息</p>
          <van-cell title="员工编号:" :value="info.llqUserCode" />
          <van-cell title="姓名:" :value="info.staffName" />
          <van-cell title="性别:" :value="info.sex" />
          <div>
            <van-cell v-if="info.papertype == '1'" title="证件类型:" value="身份证" />
            <van-cell v-else-if="info.papertype == '2'" title="证件类型:" value="护照" />
            <van-cell v-else title="证件类型:" value="其他" />
          </div>

          <van-cell title="证件号码:" :value="info.paperNum" />
          <van-cell title="手机号:" :value="info.telPhone" />
          <van-cell title="工作电话:" :value="info.companyPhone" />
          <van-cell title="微信号:" :value="info.wx" />
          <van-cell title="工作邮箱:" :value="info.inEmail" />
          <van-cell title="QQ号:" :value="info.qq" />
          <van-cell title="个人邮箱:" :value="info.ncEmail" />
          <van-cell title="家庭住址:" :value="info.address" />
          <van-cell title="生日:" :value="info.birthday" />
          <div>
            <van-cell v-if="info.marriageorno == '1'" title="婚育情况:" value="未婚" />
            <van-cell v-else-if="info.marriageorno == '2'" title="婚育情况:" value="已婚" />
            <van-cell v-else-if="info.marriageorno == '3'" title="婚育情况:" value="已婚育" />
            <van-cell v-else title="婚育情况:" value=" " />
          </div>

          <van-cell title="年龄:" :value="info.age" />
          <van-cell title="政治面貌:" :value="info.ncFace" />
          <van-cell title="民族:" :value="info.ethnic" />

          <van-cell title="工龄:" :value="dateQuitDiff(info.joinDt) + '天'" />

          <van-cell title="籍贯:" :value="info.comefrom" />

          <van-cell title="服装尺寸:" value="无" />

          <van-cell title="紧急联系人姓名:" value="" />
          <van-cell title="紧急联系人关系:" value="" />
          <div>
            <van-cell v-if="info.sosTel == null" title="紧急联系人电话:" value="" />
            <van-cell v-else title="紧急联系人电话:" :value="info.sosTel" />
          </div>
          <van-cell title="紧急联系人地址:" :value="info.address" />
          <van-cell title="归属公司:" :value="info.enCompany" />
          <van-cell title="所属部门:" :value="info.ncDepartmentCode_dictText" />
          <van-cell title="岗位:" :value="info.positioncode_dictText" />
          <van-cell title="体系:" :value="info.ncSetup" />
          <van-cell title="职级:" :value="info.ncLevel" />

          <van-cell v-if="info.grading == '98'" title="职等:" :value="'待定'" />
          <van-cell v-else-if="info.grading == '99'" title="职等:" :value="'挂靠'" />
          <van-cell v-else-if="info.grading == null || info.grading == undefined" title="职等:" :value="''" />
          <van-cell v-else-if="info.grading == '待定' || info.grading == '挂靠'" title="职等:" :value="info.grading" />
          <van-cell v-else title="职等:" :value="'L' + info.grading" />

          <van-cell title="工作地点:" :value="info.workPlace" />
          <van-cell title="考勤编号:" value="无" />
          <van-cell title="介绍人:" value="无" />
          <van-cell title="招聘渠道:" :value="info.introducePeople" />
          <van-cell title="师傅:" :value="info.tutor" />
          <van-cell title="关系说明:" :value="info.relationMsg" />
        </div>
      </van-tab>
      <van-tab title="工作信息" name="2">
        <div style="margin-left:2%;margin-top:2%;">
          <!-- <p style="font-weight: 900;font-size: 16px;">工作历程</p>
          <div>
            至今在{{ info.enCompany }}工作了
            <span style="font-size: 22px;color: red;">
              {{ dateQuitDiff(info.joinDt) + "天" }}
            </span>
          </div> -->
          <p style="font-weight: 900;font-size: 16px;">工作信息</p>
          <van-cell title="工作性质:" value="全职" />
          <div>
            <van-cell v-if="info.placeorno == '0'" title="员工状态:" value="在职" />
            <van-cell v-else-if="info.placeorno == '1'" title="员工状态:" value="产假" />
            <van-cell v-else-if="info.placeorno == '2'" title="员工状态:" value="长假" />
            <van-cell v-else title="员工状态:" value="在岗" />
          </div>

          <div>
            <van-cell v-if="info.arrDt == null" title="到职日期:" value="" />
            <van-cell v-else title="到职日期:" :value="info.arrDt" />
          </div>
          <div>
            <van-cell v-if="info.categories == '普通员工'" title="试用期:" value="6个月" />
            <van-cell v-else title="试用期:" value="3个月" />
          </div>
          <van-cell title="进司日期:" :value="info.joinDt" />
          <van-cell title="司龄:" :value="dateQuitDiff(info.joinDt) + '天'" />
          <van-cell title="转正日期:" :value="info.positiveTime" />
          <van-cell title="合同类型:" :value="info.contractType" />
          <van-cell title="合同生效日期:" :value="info.contractSdt" />
          <van-cell title="合同期限:" :value="info.contractPeriod" />
          <van-cell title="合同终止日期:" :value="info.contractEdt" />
          <van-cell title="是否延续性合同:" :value="info.contractDelay" />
          <van-cell title="延续后的终止期:" :value="info.contractEperiod" />
          <van-cell title="费用归属公司:" :value="info.enCompany" />
          <van-cell title="聘书生效日期:" :value="info.letterStartTime" />
          <van-cell title="聘书终止日期:" :value="info.letterEndTime" />
        </div>
      </van-tab>
      <van-tab title="教育背景" name="3">
        <ul>
          <li v-for="(item, index) in eduBackground">
            <div style="font-weight: 600;font-size: 18px;text-align: center;">
              {{ "教育经历" + (index + 1) }}
            </div>
            <van-cell title="毕业学校:" :value="item.school" />
            <van-cell title="专业:" :value="item.professional" />
            <van-cell title="学历:" :value="item.education" />
            <van-cell title="毕业时间:" :value="item.levelSchooldt" />
            <van-cell title="学历类型:" :value="item.educationMsg" />
          </li>
        </ul>
      </van-tab>
      <van-tab title="家庭关系" name="4">
        <div style="margin-left:2%;margin-top:2%;">
          <ul>
            <li v-for="(item, index) in family">
              <div style="font-weight: 600;font-size: 18px;text-align: center;">
                {{ "家庭关系" + (index + 1) }}
              </div>
              <van-cell title="关系:" :value="item.gx" />
              <van-cell title="姓名:" :value="item.xm" />
              <van-cell title="电话:" :value="item.tel" />
              <van-cell title="工作单位:" :value="item.gzdw" />
              <van-cell title="年龄:" :value="item.nl" />
              <van-cell title="职务:" :value="item.zw" />
            </li>
          </ul>
        </div>
      </van-tab>
      <van-tab title="工作关系" name="16">
        <div class="relationChart" ref="relationChart" id="relationChart" style="width: 100vw;height: 20rem;"></div>
        <div style="margin-left:2%;margin-top:2%;">
          <ul >
            <li v-for="(item, index) in relationshipsData">
              <div style="font-weight: 600;font-size: 18px;text-align: center;">
                {{ "工作关系" + (index + 1) }}
              </div>
              <van-cell title="姓名:" :value="item.userName" />
              <van-cell title="编码:" :value="item.userCode" />
              <van-cell title="部门:" :value="item.department" />
              <van-cell title="岗位:" :value="item.position" />
              <van-cell title="关系:" :value="item.relationship" />
            </li>
          </ul>
         
        </div>

      </van-tab>
      <van-tab title="生育信息" name="15">
        <div style="margin-left:2%;margin-top:2%;">
          <ul>
            <li v-for="(item, index) in fertilityInfo" :key="index">
              <div style="font-weight: 600;font-size: 18px;text-align: center;">
                {{ "生育信息" + (index + 1) }}
              </div>
              <van-cell title="怀孕备案时间:" :value="item.pregnancyTime" />
              <van-cell title="分娩备案时间:" :value="item.deliverTime" />
              <van-cell title="哺乳截止备案时间:" :value="item.breastfeedingTime" />
              <van-cell title="部门领导:" :value="item.deptLeader" />
              <van-cell title="备注:" :value="item.remarks" />
            </li>
          </ul>
        </div>
      </van-tab>
      <van-tab title="奖惩考核" name="5">
        <ul>
          <li v-for="(item, index) in assess">
            <div style="font-weight: 600;font-size: 18px;text-align: center;">
              {{ "奖惩" + (index + 1) }}
            </div>
            <van-cell title="时间:" :value="item.cdate" />
            <van-cell title="事由:" :value="item.msg" />
            <van-cell title="已核算:" :value="item.outputState" />
            <van-cell title="kpi扣分数:" :value="item.kpiPoints" />
            <van-cell title="上交隆爱基金:" :value="item.llqMoney" />
            <van-cell title="奖励金额:" :value="item.llqReward" />
            <van-cell title="手机话费扣款:" :value="item.phonePoints" />
            <van-cell title="经办人:" :value="item.fromPeople" />
            <van-cell title="备注:" :value="item.remark" />
          </li>
        </ul>
      </van-tab>
      <van-tab title="材料附件" name="10">

        <van-popup v-model="pdfShow" :close-on-popstate="true" :closeable="true"
          :style="{ height: '90%', width: '90%' }" @close="pdfPopupClose">
          <div :style="{ cursor: 'pointer', transform: `rotate(${rotate}deg)`, width: `100%` }">
            <pdf :page="pageNum" @num-pages="pageTotalNum = $event"
              :src="'http://service.colori.com/jeecg-boot/sys/common/static/' + fileName"></pdf>
          </div>
          <van-row>
            <van-col span="24">
              <h2 style="text-align:center">{{ pageNum }}/{{ pageTotalNum }}</h2>
            </van-col>
          </van-row>
          <van-row>
            <van-col span="6">
              <van-button type="default" @click="prePage">上一页</van-button>
            </van-col>
            <van-col span="6">
              <van-button type="default" @click="nextPage">下一页</van-button>
            </van-col>
            <van-col span="6">
              <van-button type="default" @click="handleRotate(1)">逆时针</van-button>
            </van-col>
            <van-col span="6">
              <van-button type="default" @click="handleRotate(2)">顺时针</van-button>
            </van-col>
          </van-row>
        </van-popup>

        <van-popup v-model="videoShow" :close-on-popstate="true" :closeable="true"
          :style="{ height: '50%', width: '90%' }">
          <div style="margin:30% auto">
            <video preload="auto" controls controlslist="nodownload" style="width:100%;" :src="videoName">
              您的浏览器不支持 video 标签。
            </video>
          </div>
        </van-popup>
        <van-collapse v-model="activeNames">
          <van-collapse-item title="晋升附件" name="1">
            <div v-for="item in fileAffixList['晋升']">
              <div v-if="item.fileName.indexOf(',') !== -1">
                <div v-for="v in item.fileName.split(',')">
                  <van-cell :value="v" @click="pdfPopupShow(v)" />
                </div>
              </div>
              <div v-else>
                <van-cell :value="item.fileName" @click="pdfPopupShow(item.fileName)" />
              </div>
            </div>
          </van-collapse-item>
          <van-collapse-item title="身份附件" name="2">
            <div v-for="item in fileAffixList['身份']">
              <div v-if="item.fileName.indexOf(',') !== -1">
                <div v-for="v in item.fileName.split(',')">
                  <van-cell :value="v" @click="pdfPopupShow(v)" />
                </div>
              </div>
              <div v-else>
                <van-cell :value="item.fileName" @click="pdfPopupShow(item.fileName)" />
              </div>
            </div>
          </van-collapse-item>
          <van-collapse-item title="体检附件" name="3">
            <div v-for="item in fileAffixList['体检']">
              <div v-if="item.fileName.indexOf(',') !== -1">
                <div v-for="v in item.fileName.split(',')">
                  <van-cell :value="v" @click="pdfPopupShow(v)" />
                </div>
              </div>
              <div v-else>
                <van-cell :value="item.fileName" @click="pdfPopupShow(item.fileName)" />
              </div>
            </div>
          </van-collapse-item>
          <van-collapse-item title="简历附件" name="4">
            <div v-for="item in fileAffixList['简历']">
              <div v-if="item.fileName.indexOf(',') !== -1">
                <div v-for="v in item.fileName.split(',')">
                  <van-cell :value="v" @click="pdfPopupShow(v)" />
                </div>
              </div>
              <div v-else>
                <van-cell :value="item.fileName" @click="pdfPopupShow(item.fileName)" />
              </div>
            </div>
          </van-collapse-item>
          <van-collapse-item title="离职审批附件" name="5">
            <div v-for="item in fileAffixList['离职审批']">
              <div v-if="item.fileName.indexOf(',') !== -1">
                <div v-for="v in item.fileName.split(',')">
                  <van-cell :value="v" @click="pdfPopupShow(v)" />
                </div>
              </div>
              <div v-else>
                <van-cell :value="item.fileName" @click="pdfPopupShow(item.fileName)" />
              </div>
            </div>
          </van-collapse-item>
          <van-collapse-item title="学历附件" name="6">
            <div v-for="item in fileAffixList['学历']">
              <div v-if="item.fileName.indexOf(',') !== -1">
                <div v-for="v in item.fileName.split(',')">
                  <van-cell :value="v" @click="pdfPopupShow(v)" />
                </div>
              </div>
              <div v-else>
                <van-cell :value="item.fileName" @click="pdfPopupShow(item.fileName)" />
              </div>
            </div>
          </van-collapse-item>
          <van-collapse-item title="合同附件" name="7">
            <div v-for="item in fileAffixList['合同']">
              <div v-if="item.fileName.indexOf(',') !== -1">
                <div v-for="v in item.fileName.split(',')">
                  <van-cell :value="v" @click="pdfPopupShow(v)" />
                </div>
              </div>
              <div v-else>
                <van-cell :value="item.fileName" @click="pdfPopupShow(item.fileName)" />
              </div>
            </div>
          </van-collapse-item>
          <van-collapse-item title="内部转岗交流视频" name="8">
            <div v-for="item in videoList['transfer']">
              <van-cell :value="item.name" @click="videoPopupShow(item.imageUrl)" />
            </div>
          </van-collapse-item>
          <van-collapse-item title="转正自述" name="9">
            <div v-for="item in videoList['regularization']">
              <van-cell :value="item.name" @click="videoPopupShow(item.imageUrl)" />
            </div>
          </van-collapse-item>
          <van-collapse-item title="干部续聘交流" name="10">
            <div v-for="item in videoList['continues']">
              <van-cell :value="item.name" @click="videoPopupShow(item.imageUrl)" />
            </div>
          </van-collapse-item>
          <van-collapse-item title="年度干部交流视频" name="11">
            <div v-for="item in videoList['communication']">
              <van-cell :value="item.name" @click="videoPopupShow(item.imageUrl)" />
            </div>
          </van-collapse-item>
          <van-collapse-item title="面试交流视频" name="12">
            <div v-for="item in videoList['interview']">
              <van-cell :value="item.name" @click="videoPopupShow(item.imageUrl)" />
            </div>

          </van-collapse-item>
          <van-collapse-item title="晋升交流视频" name="13">
            <div v-for="item in videoList['promotion']">
              <van-cell :value="item.name" @click="videoPopupShow(item.imageUrl)" />
            </div>
          </van-collapse-item>
        </van-collapse>
      </van-tab>
      <van-tab title="相关合同" name="6">
        <ul>
          <li v-for="(item, index) in contract">
            <div style="font-weight: 600;font-size: 18px;text-align: center;">
              {{ "合同" + (index + 1) }}
            </div>
            <van-cell title="合同生效日期:" :value="item.contractSdt" />
            <van-cell title="合同终止日期:" :value="item.contractEdt" />
            <van-cell title="延续后的终止期:" :value="item.contractEperiod" />
            <van-cell title="是否延续性合同:" value="" />
            <van-cell title="费用归属公司:" :value="item.enCompany" />
            <van-cell title="创建人:" :value="item.creator" />
            <van-cell title="创建时间:" :value="item.creDt" />
          </li>
        </ul>
      </van-tab>
      <van-tab title="岗位调整" name="7">
        <ul>
          <li v-for="(item, index) in workRecord">
            <div style="font-weight: 600;font-size: 18px;text-align: center;">
              {{ "合同" + (index + 1) }}
            </div>
            <van-cell title="调整前部门名称:" :value="item.lastNcDepartmentCode" />
            <van-cell title="调整前职位名称:" :value="item.lastPositioncode" />
            <div>
              <van-cell v-if="item.lastNclevel == '98'" title="调整前职级:" value="待定" />
              <van-cell v-if="item.lastNclevel == '99'" title="调整前职级:" value="挂靠" />
              <van-cell v-if="item.lastNclevel == null || undefined" title="调整前职级:" value="" />
              <van-cell v-else title="调整前职级:" :value="'G' + item.lastNclevel" />
            </div>
            <div>
              <van-cell v-if="item.lastGrading == null || undefined" title="调整前职等:" value="" />
              <van-cell v-else title="调整前职等:" :value="'L' + item.lastGrading" />
            </div>
            <van-cell title="调整后部门名称:" :value="item.ncDepartmentCode" />
            <van-cell title="调整后职位名称:" :value="item.positioncode" />
            <van-cell title="调整类型:" :value="item.ctype" />
            <div>
              <van-cell v-if="item.ncLevel == '98'" title="调整后职级:" value="待定" />
              <van-cell v-if="item.ncLevel == '99'" title="调整后职级:" value="挂靠" />
              <van-cell v-if="item.ncLevel == null || undefined" title="调整后职级:" value="" />
              <van-cell v-else title="调整后职级:" :value="'G' + item.ncLevel" />
            </div>
            <div>
              <van-cell v-if="item.grading == null || undefined" title="调整后职等:" value="" />
              <van-cell v-else title="调整后职等:" :value="'L' + item.grading" />
            </div>
            <van-cell title="操作人:" :value="item.opr" />
            <van-cell title="操作时间:" :value="item.optTime" />
          </li>
        </ul>
      </van-tab>
      <van-tab title="工作记录" name="8">
        <ul>
          <li v-for="(item, index) in workExperience">
            <div style="font-weight: 600;font-size: 18px;text-align: center;">
              {{ "工作记录" + (index + 1) }}
            </div>
            <van-cell title="起始年月:" :value="item.bdate" />
            <van-cell title="结束年月:" :value="item.edate" />
            <van-cell title="工作单位:" :value="item.comp" />
            <van-cell title="职务:" :value="item.zhiwu" />
            <van-cell title="证明人:" :value="item.zmr" />
            <van-cell title="联系电话:" :value="item.lxdh" />
          </li>
        </ul>
      </van-tab>
      <van-tab title="聘书记录" name="9">
        <ul>
          <li v-for="(item, index) in appointment">
            <div style="font-weight: 600;font-size: 18px;text-align: center;">
              {{ "聘书记录" + (index + 1) }}
            </div>
            <van-cell title="起始年月:" :value="item.letterStartTime" />
            <van-cell title="结束年月:" :value="item.letterEndTime" />
            <van-cell title="创建人:" :value="item.creator" />
            <van-cell title="创建时间:" :value="item.createTime" />
          </li>
        </ul>
      </van-tab>
      <van-tab title="人员评测" name="14" :disabled='!hasEvaluationPermission'>
        <div v-show="vdefShow" class="radarChart" ref="radarChart" id="radarChart" style="width: 100vw;height: 20rem;">
        </div>
        <div v-show="manageShow" class="manageChart" ref="manageChart" id="manageChart"
          style="width: 100vw;height: 20rem;"></div>
        <!-- 人员测评表 -->
        <van-collapse v-model="collapseActiveNames">
          <van-collapse-item title="人员测评表" name="1">
            <div v-for="(item, index) in evaluationList" :key="index" style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
              <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                  <van-col span="24">
                    <span style="font-size: 18px; font-weight: 700; color: #000">
                      {{ item.staffName }}-{{ item.userCode }}
                    </span>
                  </van-col>
                </van-row>
              </div>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 部门：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ item.departmentName }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 岗位：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ item.positionName }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <div v-show="vdefShow">
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 可靠性：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.vdef1 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 亲和力：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.vdef2 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 自我程度：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.vdef3 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 担当：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.vdef4 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 工作投入程度：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.vdef5 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 成就动机：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.vdef6 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 稳定：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.vdef7 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
              </div>
              <div v-show="manageShow">
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 制定目标：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.manage1 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 组织工作：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.manage2 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 沟通交流：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.manage3 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 绩效评估：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.manage4 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
                <van-row>
                  <van-col span="24" style="color: gary">
                    <van-row>
                      <van-col span="8"> 人员培养：</van-col>
                      <van-col span="16">
                        <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                          {{ item.manage5 }}
                        </span>
                      </van-col>
                    </van-row>
                  </van-col>
                </van-row>
              </div>

            </div>
          </van-collapse-item>
          <van-collapse-item title="人员品性评估" name="2" v-show="noteShow">
            <div style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
              <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                  <van-col span="24">
                    <span style="font-size: 18px; font-weight: 700; color: #000">
                      电子游戏
                    </span>
                  </van-col>
                </van-row>
              </div>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 经常：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note1.jingchang }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 偶尔：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note1.ouer }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 无：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note1.wu }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 不了解：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note1.blj }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
            </div>
            <div style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
              <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                  <van-col span="24">
                    <span style="font-size: 18px; font-weight: 700; color: #000">
                      吸烟
                    </span>
                  </van-col>
                </van-row>
              </div>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 频繁：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note2.pinfan }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 偶尔：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note2.ouer }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 无：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note2.wu }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 不了解：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note2.blj }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
            </div>
            <div style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
              <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                  <van-col span="24">
                    <span style="font-size: 18px; font-weight: 700; color: #000">
                      向同事借钱
                    </span>
                  </van-col>
                </van-row>
              </div>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 经常：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note7.jingchang }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 偶尔：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note7.ouer }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 无：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note7.wu }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 不了解：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note7.blj }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
            </div>
            <div style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
              <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                  <van-col span="24">
                    <span style="font-size: 18px; font-weight: 700; color: #000">
                      赌博
                    </span>
                  </van-col>
                </van-row>
              </div>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 经常：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note3.jingchang }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 偶尔：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note3.ouer }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 无：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note3.wu }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 不了解：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note3.blj }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
            </div>
            <div style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
              <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                  <van-col span="24">
                    <span style="font-size: 18px; font-weight: 700; color: #000">
                      打架斗殴
                    </span>
                  </van-col>
                </van-row>
              </div>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 有被拘案史：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note4.ybjas }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 有被处罚经历：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note4.ybcfjl }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 无：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note4.wu }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 不了解：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note4.blj }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
            </div>
            <div style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
              <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                  <van-col span="24">
                    <span style="font-size: 18px; font-weight: 700; color: #000">
                      喝酒
                    </span>
                  </van-col>
                </van-row>
              </div>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 常喝：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note5.changhe }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 偶尔：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note5.ouer }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 无：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note5.wu }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 不了解：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note4.blj }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
            </div>
            <div style="
        text-align: left;
        background-color: #f5f5f5;
        padding: 3%;
        border-radius: 10px;
        width: 95%;
        margin: 0.3rem auto;
        margin-bottom: 3%;
      ">
              <div class="van-hairline--bottom" style="margin-bottom: 0.3rem">
                <van-row>
                  <van-col span="24">
                    <span style="font-size: 18px; font-weight: 700; color: #000">
                      言语粗暴
                    </span>
                  </van-col>
                </van-row>
              </div>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 态度严厉：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note6.taiduyanli }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 侮辱人格：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note6.wururenge }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 无：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note6.wu }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="24" style="color: gary">
                  <van-row>
                    <van-col span="8"> 不了解：</van-col>
                    <van-col span="16">
                      <span style="
                  color: black;
                  width: 100%;
                  word-wrap: break-word;
                  word-break: break-all;
                  overflow: hidden;
                ">
                        {{ computeRes.note4.blj }}
                      </span>
                    </van-col>
                  </van-row>
                </van-col>
              </van-row>
            </div>
          </van-collapse-item>
          <van-collapse-item title="评估结果汇总" name="3" v-show="vdefShow">
            <div>

              <div>
                1、可靠性{{ Math.round(averages.vdef1 * 1 + averages.vdef2 * 1 + averages.vdef3 * 1) }}
              </div>
              <div
                :style="{ color: Math.round(averages.vdef1 * 1 + averages.vdef2 * 1 + averages.vdef3 * 1) >= 3 && Math.round(averages.vdef1 * 1 + averages.vdef2 * 1 + averages.vdef3 * 1) <= 6 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;3分-6分&emsp;&nbsp;&nbsp;&nbsp;&nbsp;做事不靠谱
              </div>
              <div
                :style="{ color: Math.round(averages.vdef1 * 1 + averages.vdef2 * 1 + averages.vdef3 * 1) >= 7 && Math.round(averages.vdef1 * 1 + averages.vdef2 * 1 + averages.vdef3 * 1) <= 11 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;7分-11分&emsp;&nbsp;&nbsp;做事一般靠谱
              </div>
              <div
                :style="{ color: Math.round(averages.vdef1 * 1 + averages.vdef2 * 1 + averages.vdef3 * 1) >= 12 && Math.round(averages.vdef1 * 1 + averages.vdef2 * 1 + averages.vdef3 * 1) <= 15 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;12分-15分&emsp;做事靠谱
              </div>
            </div>


            <div>
              <div>
                2、责任{{ Math.round(averages.vdef4) }}
              </div>
              <div
                :style="{ color: Math.round(averages.vdef4 * 1) >= 1 && Math.round(averages.vdef4 * 1) <= 2 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;1分-2分&emsp;不担当，不扛责
              </div>
              <div :style="{ color: Math.round(averages.vdef4 * 1) == 3 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;3分&emsp;&emsp;&emsp;能担当职责范围的工作，但能力需有所提升
              </div>
              <div
                :style="{ color: Math.round(averages.vdef4 * 1) >= 4 && Math.round(averages.vdef4 * 1) <= 5 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;4分-5分&emsp;能担当，能扛责
              </div>
            </div>

            <div>

              <div>
                3、意愿{{ Math.round(averages.vdef5 * 1 + averages.vdef6 * 1) }}
              </div>
              <div
                :style="{ color: Math.round(averages.vdef5 * 1 + averages.vdef6 * 1) >= 2 && Math.round(averages.vdef5 * 1 + averages.vdef6 * 1) <= 4 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;2分-4分&emsp;&nbsp;&nbsp;&nbsp;&nbsp;满足现状，无晋升动机或动机不强
              </div>
              <div
                :style="{ color: Math.round(averages.vdef5 * 1 + averages.vdef6 * 1) >= 5 && Math.round(averages.vdef5 * 1 + averages.vdef6 * 1) <= 7 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;5分-7分&emsp;&nbsp;&nbsp;&nbsp;&nbsp;有晋升动机，但缺乏一定行动力
              </div>
              <div
                :style="{ color: Math.round(averages.vdef5 * 1 + averages.vdef6 * 1) >= 8 && Math.round(averages.vdef5 * 1 + averages.vdef6 * 1) <= 10 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;8分-10分&emsp;&nbsp;&nbsp;晋升动机强烈，且有行动
              </div>
            </div>

            <div>
              <div>
                4、稳定{{ Math.round(averages.vdef7 * 1) }}
              </div>
              <div
                :style="{ color: Math.round(averages.vdef7 * 1) >= 1 && Math.round(averages.vdef7 * 1) <= 2 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;1分-2分&emsp;不稳定，会受外界因素或自身选择影响较强
              </div>
              <div :style="{ color: Math.round(averages.vdef7 * 1) == 3 ? 'red' : '' }" style="word-break:no-break">
                &emsp;&nbsp;&nbsp;3分&emsp;&emsp;&emsp;稳定性一般，会被外因有影响，但不强（预估有2-3年稳定期）
              </div>
              <div
                :style="{ color: Math.round(averages.vdef7 * 1) >= 4 && Math.round(averages.vdef7 * 1) <= 5 ? 'red' : '' }">
                &emsp;&nbsp;&nbsp;4分-5分&emsp;稳定性好（预估有5年以上期可稳定发展）
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { Toast } from 'vant'; // 新增导入 Toast
import pdf from 'vue-pdf'

// 引入需要的图表类型，柱状图和雷达图
require('echarts/lib/chart/radar')
// 引入提示框和title组件，图例
require('echarts/lib/component/tooltip')
require('echarts/lib/component/title')
require('echarts/lib/component/legend')
const echarts = require('echarts/lib/echarts')

export default {
  components: {
    pdf,
  },
  data() {
    return {
      collapseActiveNames: ['1', '2', '3'],
      activeNames: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"],
      activeName: "1",
      info: {},
      family: [],
      fertilityInfo: [],
      fileList: [],
      assess: [],
      workRecord: [],
      eduBackground: [],
      workExperience: [],
      appointment: [],
      contract: [],

      fileAffixList: {},
      videoList: {},
      pdfShow: false,
      fileName: '',
      videoShow: false,
      videoName: '',

      pageNum: 1,
      pageTotalNum: 1,
      rotate: 0,
      rotateAdd: 90,

      radarChart: null,
      evaluationList: [],
      averages: {},
      computeRes: {
        note1: {
          jingchang: 0,
          ouer: 0,
          wu: 0,
          pinfan: 0,
          ybjas: 0,
          ybcfjl: 0,
          changhe: 0,
          taiduyanli: 0,
          wururenge: 0,
          blj: 0,
        }, note2: {
          jingchang: 0,
          ouer: 0,
          wu: 0,
          pinfan: 0,
          ybjas: 0,
          ybcfjl: 0,
          changhe: 0,
          taiduyanli: 0,
          wururenge: 0,
          blj: 0,
        }, note3: {
          jingchang: 0,
          ouer: 0,
          wu: 0,
          pinfan: 0,
          ybjas: 0,
          ybcfjl: 0,
          changhe: 0,
          taiduyanli: 0,
          wururenge: 0,
          blj: 0,
        }, note4: {
          jingchang: 0,
          ouer: 0,
          wu: 0,
          pinfan: 0,
          ybjas: 0,
          ybcfjl: 0,
          changhe: 0,
          taiduyanli: 0,
          wururenge: 0,
          blj: 0,
        }, note5: {
          jingchang: 0,
          ouer: 0,
          wu: 0,
          pinfan: 0,
          ybjas: 0,
          ybcfjl: 0,
          changhe: 0,
          taiduyanli: 0,
          wururenge: 0,
          blj: 0,
        }, note6: {
          jingchang: 0,
          ouer: 0,
          wu: 0,
          pinfan: 0,
          ybjas: 0,
          ybcfjl: 0,
          changhe: 0,
          taiduyanli: 0,
          wururenge: 0,
          blj: 0,
        }, note7: {
          jingchang: 0,
          ouer: 0,
          wu: 0,
          pinfan: 0,
          ybjas: 0,
          ybcfjl: 0,
          changhe: 0,
          taiduyanli: 0,
          wururenge: 0,
          blj: 0,
        }
      },
      hasEvaluationPermission: false,

      manageChart: null,
      vdefShow: false,
      noteShow: false,
      manageShow: false,
      manageAverages: {},
      relationChart: null,
      relationshipsData: []
    };
  },
  created() {
    this.$axios
      .get(`/jeecg-boot/app/login/getUserPermissionByToken?userCode=${localStorage.getItem('userCode')}`)
      .then(res => {
        if (res.data.code == 200) {
          this.hasEvaluationPermission = JSON.stringify(res.data.result.auth).indexOf('evaluation:see')>-1
        }
      });
    this.info = this.$route.params.item;
    //照片
    this.fileList = `http://service.colori.com/jeecg-boot/sys/common/static/temp/${this.info.llqUserCode}.JPG`;

    // 文件附件
    this.$axios
      .get(`/jeecg-boot/staff/file/getDetail?ncStaffCode=${this.info.ncStaffCode}`)
      .then(res => {
        if (res.data.code == 200) {
          console.log('文件附件', res.data.result["体检"][0].fileName);
          this.fileAffixList = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    // 视频附件
    this.$axios
      .get(`/jeecg-boot/staff/file/getVideoInfo?llqUserCode=${this.info.llqUserCode}`)
      .then(res => {
        if (res.data.code == 200) {
          console.log('视频附件', res.data.result);
          this.videoList = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });

    //家庭关系
    this.$axios
      .get(`/jeecg-boot/app/staff/getFamily?paperNum=${this.info.paperNum}`)
      .then(res => {
        if ((res.data.code == 200)) {
          this.family = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //奖惩情况
    this.$axios
      .get(`/jeecg-boot/app/staff/getRp?llqUserCode=${this.info.llqUserCode}`)
      .then(res => {
        if ((res.data.code == 200)) {
          this.assess = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //相关合同
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getContract?llqUserCode=${this.info.llqUserCode}`
      )
      .then(res => {
        if ((res.data.code == 200)) {
          this.contract = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //工作记录
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getWorkExperience?paperNum=${this.info.paperNum}`
      )
      .then(res => {
        if ((res.data.code == 200)) {
          this.workExperience = res.data.result;
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //教育水平
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getEduBackground?llqUserCode=${this.info.llqUserCode}`
      )
      .then(res => {
        if ((res.data.code == 200)) {
          this.eduBackground = res.data.result;
          console.log(this.eduBackground);
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //工作历程
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getEduBackground?llqUserCode=${this.info.llqUserCode}`
      )
      .then(res => {
        if ((res.data.code == 200)) {
          this.eduBackground = res.data.result;
          console.log(this.eduBackground);
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });
    //聘书记录
    this.$axios
      .get(
        `/jeecg-boot/app/staff/getAppointment?llqUserCode=${this.info.llqUserCode}`
      )
      .then(res => {
        if ((res.data.code == 200)) {
          this.appointment = res.data.result;
          console.log(this.eduBackground);
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      });

    // 新增：获取生育信息
    this.$axios
      .get(`/jeecg-boot/app/ncStaffFertility/list?llqUserCode=${this.info.llqUserCode}`)
      .then(res => {
        if (res.data.code == 200) {
          this.fertilityInfo = res.data.result.records;
        } else {
          Toast({
            message: res.data.msg || "获取生育信息失败",
            position: "bottom",
            duration: 2000
          });
        }
      })
      .catch(error => {
        Toast({
          message: "请求生育信息接口失败",
          position: "bottom",
          duration: 2000
        });
        console.error("获取生育信息失败:", error);
      });
  },
  methods: {
    queryRelationInfo() {
      this.$axios.get(`/jeecg-boot/staff/ncStaffRelationship/list?ncStaffCode=${this.info.ncStaffCode}`).then(res => {
        if (res.data.code == 200) {
          this.relationshipsData = res.data.result.records;
          let links = []
          let data = [
            {
              name: this.info.staffName,
              tooltip: {
                formatter: `{b} <br/>
                部门：${this.info.ncDepartmentName}<br/>
                岗位：${this.info.positionName}`,
              },
              itemStyle: {
                normal: {
                  color: '#FCBB5B',
                  borderColor: '#FCBB5B',
                  shadowColor: '#FCBB5B',
                },
              },
            },
          ]
          this.relationshipsData.forEach((item, index) => {
            data.push({
              name: item.userName,
              tooltip: {
                formatter: `{b} <br/>
                部门：${item.department}<br/>
                岗位：${item.position}`,
              },
              itemStyle: {
                normal: {
                  color: '#29ACFC',
                  borderColor: '#29ACFC',
                  shadowColor: '#29ACFC'
                }
              }
            })
            links.push({
              source: 0,
              target: index + 1,
              value: item.relationship,
              lineStyle: {
                normal: {
                  color: '#666',
                  width: 1
                }
              }
            })
          })

          this.relationChart.setOption( {
  title: {
    text: 'Basic Graph'
  },
  tooltip: {},
  animationDurationUpdate: 1500,
  animationEasingUpdate: 'quinticInOut',
  series: [
    {
      type: 'graph',
      layout: 'none',
      symbolSize: 50,
      roam: true,
      label: {
        show: true
      },
      edgeSymbol: ['circle', 'arrow'],
      edgeSymbolSize: [4, 10],
      edgeLabel: {
        fontSize: 20
      },
      data: [
        {
          name: 'Node 1',
          x: 300,
          y: 300
        },
        {
          name: 'Node 2',
          x: 800,
          y: 300
        },
        {
          name: 'Node 3',
          x: 550,
          y: 100
        },
        {
          name: 'Node 4',
          x: 550,
          y: 500
        }
      ],
      // links: [],
      links: [
        {
          source: 0,
          target: 1,
          symbolSize: [5, 20],
          label: {
            show: true
          },
          lineStyle: {
            width: 5,
            curveness: 0.2
          }
        },
        {
          source: 'Node 2',
          target: 'Node 1',
          label: {
            show: true
          },
          lineStyle: {
            curveness: 0.2
          }
        },
        {
          source: 'Node 1',
          target: 'Node 3'
        },
        {
          source: 'Node 2',
          target: 'Node 3'
        },
        {
          source: 'Node 2',
          target: 'Node 4'
        },
        {
          source: 'Node 1',
          target: 'Node 4'
        }
      ],
      lineStyle: {
        opacity: 0.9,
        width: 2,
        curveness: 0
      }
    }
  ]
})
        } else {
          Toast({
            message: res.data.msg,
            position: "bottom",
            duration: 2000
          });
        }
      })
    },
    queryEvaluationInfo(llqUserCode) {
      this.$axios.get(`/jeecg-boot/app/staff/queryEvaluationInfo?userCode=${llqUserCode}`).then((res) => {
        if (res.data.success) {
          this.evaluationList = []
          this.averages = {}
          this.manageAverages = {}
          if (res.data.result.length > 0) {
            this.evaluationList = res.data.result[0].ncStaffEvaluationPersons

            if (res.data.result[0].checkInfos.indexOf('vdef') != -1) {
              this.vdefShow = true;
            }
            if (res.data.result[0].checkInfos.indexOf('note') != -1) {
              this.noteShow = true;
            }
            if (res.data.result[0].checkInfos.indexOf('manage') != -1) {
              this.manageShow = true;
            }

            // 计算每个属性的平均值，将null视为0
            let propertyCount = 0;
            let arr = this.evaluationList.filter(item => item.status == 2)
            for (let key in this.computeRes) {
              this.computeRes[key].jingchang = 0
              this.computeRes[key].ouer = 0
              this.computeRes[key].wu = 0
              this.computeRes[key].pinfan = 0
              this.computeRes[key].ybjas = 0
              this.computeRes[key].ybcfjl = 0
              this.computeRes[key].changhe = 0
              this.computeRes[key].taiduyanli = 0
              this.computeRes[key].wururenge = 0
              this.computeRes[key].blj = 0
            }

            arr.forEach(obj => {
              if (obj.note1 == "经常") {
                this.computeRes.note1.jingchang++;
              } else if (obj.note1 == "偶尔") {
                this.computeRes.note1.ouer++;
              } else if (obj.note1 == "无") {
                this.computeRes.note1.wu++;
              } else if (obj.note1 == "不了解") {
                this.computeRes.note1.blj++;
              }
              if (obj.note2 == "频繁") {
                this.computeRes.note2.pinfan++;
              } else if (obj.note2 == "偶尔") {
                this.computeRes.note2.ouer++;
              } else if (obj.note2 == "无") {
                this.computeRes.note2.wu++;
              } else if (obj.note2 == "不了解") {
                this.computeRes.note2.blj++;
              }
              if (obj.note3 == "经常") {
                this.computeRes.note3.jingchang++;
              } else if (obj.note3 == "偶尔") {
                this.computeRes.note3.ouer++;
              } else if (obj.note3 == "无") {
                this.computeRes.note3.wu++;
              } else if (obj.note3 == "不了解") {
                this.computeRes.note3.blj++;
              }
              if (obj.note4 == "有被拘案史") {
                this.computeRes.note4.ybjas++;
              } else if (obj.note4 == "有被处罚经历") {
                this.computeRes.note4.ybcfjl++;
              } else if (obj.note4 == "无") {
                this.computeRes.note4.wu++;
              } else if (obj.note4 == "不了解") {
                this.computeRes.note4.blj++;
              }
              if (obj.note5 == "常喝") {
                this.computeRes.note5.changhe++;
              } else if (obj.note5 == "偶尔") {
                this.computeRes.note5.ouer++;
              } else if (obj.note5 == "无") {
                this.computeRes.note5.wu++;
              } else if (obj.note5 == "不了解") {
                this.computeRes.note5.blj++;
              }
              if (obj.note6 == "态度严厉") {
                this.computeRes.note6.taiduyanli++;
              } else if (obj.note6 == "侮辱人格") {
                this.computeRes.note6.wururenge++;
              } else if (obj.note6 == "无") {
                this.computeRes.note6.wu++;
              } else if (obj.note6 == "不了解") {
                this.computeRes.note6.blj++;
              }
              if (obj.note7 == "经常") {
                this.computeRes.note7.jingchang++;
              } else if (obj.note7 == "偶尔") {
                this.computeRes.note7.ouer++;
              } else if (obj.note7 == "无") {
                this.computeRes.note7.wu++;
              } else if (obj.note7 == "不了解") {
                this.computeRes.note7.blj++;
              }
              console.log("🚀 ~ getAction ~ this.computeRes:", this.computeRes)


              Object.keys(obj).forEach(key => {
                if (key.startsWith('vdef')) {
                  if (!this.averages[key]) {
                    this.averages[key] = 0;
                    propertyCount++;
                  }
                  this.averages[key] += parseFloat(obj[key] === null ? 0 : obj[key]);
                }
                if (key.startsWith('manage')) {
                  if (!this.manageAverages[key]) {
                    this.manageAverages[key] = 0;
                    propertyCount++;
                  }
                  this.manageAverages[key] += parseFloat(obj[key] === null ? 0 : obj[key]);
                }
              });
            });
            Object.keys(this.averages).forEach(key => {
              this.averages[key] = (this.averages[key] / arr.length).toFixed(2);
            });
            Object.keys(this.manageAverages).forEach(key => {
              this.manageAverages[key] = (this.manageAverages[key] / arr.length).toFixed(2);
            });
          }
          this.radarChart.setOption(
            {
              title: {
                text: ''
              },
              radar: {
                // shape: 'circle',
                indicator: [
                  { name: '可靠性', max: 5 },
                  { name: '亲和力', max: 5 },
                  { name: '自我程度', max: 5 },
                  { name: '担当', max: 5 },
                  { name: '工作投入程度', max: 5 },
                  { name: '成就动机', max: 5 },
                  { name: '稳定', max: 5 },
                ]
              },
              series: [
                {
                  name: '人员评测',
                  type: 'radar',
                  data: [
                    {
                      value: [this.averages.vdef1 * 1 || 0, this.averages.vdef2 * 1 || 0, this.averages.vdef3 * 1 || 0, this.averages.vdef4 * 1 || 0, this.averages.vdef5 * 1 || 0, this.averages.vdef6 * 1 || 0, this.averages.vdef7 * 1 || 0],
                      name: '人员评测',
                      label: {
                        show: true,
                        formatter: function (params) {
                          return params.value;
                        }
                      }
                    },
                  ]
                }
              ]
            }
          )

          this.manageChart.setOption(
            {
              title: {
                text: ''
              },
              radar: {
                // shape: 'circle',
                indicator: [
                  { name: '制定目标', max: 5 },
                  { name: '组织工作', max: 5 },
                  { name: '沟通交流', max: 5 },
                  { name: '绩效评估', max: 5 },
                  { name: '人员培养', max: 5 },
                ]
              },
              series: [
                {
                  name: '管理者五项测评',
                  type: 'radar',
                  data: [
                    {
                      value: [this.manageAverages.manage1 * 1 || 0, this.manageAverages.manage2 * 1 || 0, this.manageAverages.manage3 * 1 || 0, this.manageAverages.manage4 * 1 || 0, this.manageAverages.manage5 * 1 || 0],
                      name: '管理者五项测评',
                      label: {
                        show: true,
                        formatter: function (params) {
                          return params.value;
                        }
                      }
                    },
                  ]
                }
              ]
            }
          )
        } else {
          this.evaluationList = []
          this.radarChart.setOption(
            {
              title: {
                text: '人员评测'
              },
              radar: {
                // shape: 'circle',
                indicator: [
                  { name: '可靠性', max: 5 },
                  { name: '亲和力', max: 5 },
                  { name: '自我程度', max: 5 },
                  { name: '担当', max: 5 },
                  { name: '工作投入程度', max: 5 },
                  { name: '成就动机', max: 5 },
                  { name: '稳定', max: 5 },
                ]
              },
              series: [
                {
                  name: '人员评测',
                  type: 'radar',
                  data: [
                    {
                      value: [0, 0, 0, 0, 0, 0, 0],
                      name: '人员评测'
                    },

                  ]
                }
              ]
            }
          )


          this.manageChart.setOption(
            {
              title: {
                text: ''
              },
              radar: {
                // shape: 'circle',
                indicator: [
                  { name: '制定目标', max: 5 },
                  { name: '组织工作', max: 5 },
                  { name: '沟通交流', max: 5 },
                  { name: '绩效评估', max: 5 },
                  { name: '人员培养', max: 5 },
                ]
              },
              series: [
                {
                  name: '管理者五项测评',
                  type: 'radar',
                  data: [
                    {
                      value: [0, 0, 0, 0, 0],
                      name: '管理者五项测评',
                      label: {
                        show: true,
                        formatter: function (params) {
                          return params.value;
                        }
                      }
                    },
                  ]
                }
              ]
            }
          )
        }
      })
    },
    onChange(e) {
      if (e == 14) {
        this.$nextTick(() => {
          this.radarChart = echarts.init(document.getElementById('radarChart'))
          this.manageChart = echarts.init(document.getElementById('manageChart'))
          this.queryEvaluationInfo(this.info.llqUserCode)
        })
      }
      else if(e == 16){
        this.$nextTick(() => {
          this.relationChart = echarts.init(document.getElementById('relationChart'))
          this.queryRelationInfo()
        })
      }
    },
    pdfPopupClose() {
      console.log('popup关闭');
      this.pageNum = 1;
      this.pageTotalNum = 1;
      this.rotate = 0;
      this.rotateAdd = 90;
    },
    pdfPopupShow(fileName) {
      this.fileName = fileName
      this.pdfShow = true
    },
    videoPopupShow(videoName) {
      this.videoName = videoName
      this.videoShow = true
    },
    prePage() {
      let p = this.pageNum
      p = p > 1 ? p - 1 : this.pageTotalNum
      this.pageNum = p
    },
    nextPage() {
      let p = this.pageNum
      p = p < this.pageTotalNum ? p + 1 : 1
      this.pageNum = p
    },
    handleRotate(type) {
      switch (type) {
        case 1:
          this.rotate -= this.rotateAdd;
          break;
        case 2:
          this.rotate += this.rotateAdd;
          break;
      }
    },
    dateQuitDiff(sDate1) {
      let date2 = new Date();
      let date1 = new Date(Date.parse(sDate1.replace(/-/g, "/")));
      let iDays = parseInt(
        Math.abs(date2.getTime() - date1.getTime()) / 1000 / 60 / 60 / 24
      );
      let workday = "";
      if (iDays > 365) {
        let year = Math.floor(iDays / 365);
        let days = iDays - year * 365;
        workday = year + "年" + days;
      } else {
        workday = iDays;
      }
      return workday;
    },
    onClickLeft() {
      this.$router.go(-1);
    }
  },
  beforeRouteLeave(to, from, next) {
    //设置下一个路由的meta,让列表页面缓存,即不刷新
    to.meta.keepAlive = true;
    next();
  }
};
</script>

<style scoped>
.photo {
  width: 320px;
  height: 380px;
  margin: 0 auto;
}
</style>
